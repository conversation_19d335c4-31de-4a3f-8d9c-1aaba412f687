import React from "react";
import type { Application } from "../types/index.js";

interface ApplicationCardProps {
  application: Application;
  onSelect: (app: Application) => void;
  onEdit?: (app: Application) => void;
  onDelete?: (app: Application) => void;
}

const ApplicationCard: React.FC<ApplicationCardProps> = ({
  application,
  onSelect,
  onEdit,
  onDelete,
}) => {
  return (
    <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow cursor-pointer">
      <div onClick={() => onSelect(application)}>
        <div className="flex items-center mb-4">
          {application.icon_url ? (
            <img
              src={application.icon_url}
              alt={application.name}
              className="w-12 h-12 rounded-lg mr-4"
            />
          ) : (
            <div className="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
              <span className="text-white font-bold text-lg">
                {application.name.charAt(0).toUpperCase()}
              </span>
            </div>
          )}
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              {application.name}
            </h3>
            <span className="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
              {application.app_type}
            </span>
          </div>
        </div>

        <p className="text-gray-600 mb-4 line-clamp-3">
          {application.description || "暂无描述"}
        </p>

        <div className="text-sm text-gray-500">
          创建时间: {new Date(application.created_at).toLocaleDateString()}
        </div>
      </div>

      {(onEdit || onDelete) && (
        <div className="flex justify-end mt-4 space-x-2">
          {onEdit && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onEdit(application);
              }}
              className="px-3 py-1 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded"
            >
              编辑
            </button>
          )}
          {onDelete && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onDelete(application);
              }}
              className="px-3 py-1 text-sm text-red-600 hover:text-red-800 hover:bg-red-50 rounded"
            >
              删除
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ApplicationCard;
