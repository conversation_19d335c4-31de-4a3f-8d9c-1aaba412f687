import React, { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import type { Application, ApplicationCreate } from "../types/index.js";
import { applicationApi } from "../services/api";

const ApplicationForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const isEdit = id !== "new";

  const [formData, setFormData] = useState<ApplicationCreate>({
    name: "",
    description: "",
    dify_app_id: "",
    dify_api_key: "",
    app_type: "chat",
    icon_url: "",
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isEdit && id) {
      loadApplication(parseInt(id));
    }
  }, [isEdit, id]);

  const loadApplication = async (appId: number) => {
    try {
      setLoading(true);
      const app = await applicationApi.getApplication(appId);
      setFormData({
        name: app.name,
        description: app.description,
        dify_app_id: app.dify_app_id,
        dify_api_key: "", // 不显示现有的API密钥
        app_type: app.app_type,
        icon_url: app.icon_url || "",
      });
    } catch (err) {
      setError("加载应用信息失败");
      console.error("Error loading application:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setError(null);

    try {
      if (isEdit && id) {
        // 编辑模式：只更新非空字段
        const updateData: Partial<ApplicationCreate> = {
          name: formData.name,
          description: formData.description,
          app_type: formData.app_type,
          icon_url: formData.icon_url,
        };

        // 只有在输入了新的API密钥时才更新
        if (formData.dify_api_key.trim()) {
          updateData.dify_api_key = formData.dify_api_key;
        }

        await applicationApi.updateApplication(parseInt(id), updateData);
      } else {
        // 创建模式
        await applicationApi.createApplication(formData);
      }

      navigate("/admin");
    } catch (err: any) {
      setError(err.response?.data?.detail || "保存失败");
      console.error("Error saving application:", err);
    } finally {
      setSaving(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-2xl mx-auto">
          <div className="flex items-center mb-8">
            <button
              onClick={() => navigate("/admin")}
              className="text-gray-600 hover:text-gray-800 mr-4"
            >
              ← 返回
            </button>
            <h1 className="text-3xl font-bold text-gray-900">
              {isEdit ? "编辑应用" : "创建应用"}
            </h1>
          </div>

          <div className="bg-white rounded-lg shadow p-6">
            {error && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <p className="text-red-600">{error}</p>
              </div>
            )}

            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  应用名称 *
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入应用名称"
                />
              </div>

              <div>
                <label
                  htmlFor="description"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  应用描述
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入应用描述"
                />
              </div>

              <div>
                <label
                  htmlFor="dify_app_id"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Dify应用ID *
                </label>
                <input
                  type="text"
                  id="dify_app_id"
                  name="dify_app_id"
                  value={formData.dify_app_id}
                  onChange={handleChange}
                  required={!isEdit}
                  disabled={isEdit}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                  placeholder="输入Dify应用ID"
                />
                {isEdit && (
                  <p className="mt-1 text-sm text-gray-500">
                    应用ID创建后不可修改
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="dify_api_key"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  Dify API密钥 {!isEdit && "*"}
                </label>
                <input
                  type="password"
                  id="dify_api_key"
                  name="dify_api_key"
                  value={formData.dify_api_key}
                  onChange={handleChange}
                  required={!isEdit}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={
                    isEdit ? "留空表示不修改API密钥" : "输入Dify API密钥"
                  }
                />
                {isEdit && (
                  <p className="mt-1 text-sm text-gray-500">
                    留空表示不修改现有API密钥
                  </p>
                )}
              </div>

              <div>
                <label
                  htmlFor="app_type"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  应用类型
                </label>
                <select
                  id="app_type"
                  name="app_type"
                  value={formData.app_type}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="chat">聊天助手</option>
                  <option value="completion">文本生成</option>
                  <option value="workflow">工作流</option>
                </select>
              </div>

              <div>
                <label
                  htmlFor="icon_url"
                  className="block text-sm font-medium text-gray-700 mb-2"
                >
                  图标URL
                </label>
                <input
                  type="url"
                  id="icon_url"
                  name="icon_url"
                  value={formData.icon_url}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入图标URL（可选）"
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="button"
                  onClick={() => navigate("/admin")}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
                >
                  {saving ? "保存中..." : isEdit ? "更新" : "创建"}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApplicationForm;
