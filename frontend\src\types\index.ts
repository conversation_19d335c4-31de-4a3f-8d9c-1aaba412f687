// 类型定义文件

export interface Application {
  id: number;
  name: string;
  description: string;
  dify_app_id: string;
  app_type: string;
  icon_url?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Conversation {
  id: number;
  dify_conversation_id: string;
  title: string;
  application_id: number;
  created_at: string;
  updated_at: string;
}

export interface Message {
  id: number;
  dify_message_id: string;
  content: string;
  role: 'user' | 'assistant';
  metadata?: any;
  created_at: string;
}

export interface ChatRequest {
  query: string;
  conversation_id?: string;
  inputs?: Record<string, any>;
  files?: string[];
}

export interface ChatResponse {
  message_id: string;
  conversation_id: string;
  answer: string;
  metadata?: any;
}

export interface ApplicationCreate {
  name: string;
  description: string;
  dify_app_id: string;
  dify_api_key: string;
  app_type: string;
  icon_url?: string;
}

export interface UploadedFile {
  id: number;
  filename: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  dify_file_id?: string;
  url: string;
}
