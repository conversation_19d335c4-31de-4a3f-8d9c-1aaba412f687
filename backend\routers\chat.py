"""
聊天相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import json
import os
import uuid
import shutil

from database import get_db
from models import Application, Conversation, Message, UploadedFile
from dify_service import DifyService

router = APIRouter(prefix="/api/chat", tags=["chat"])

# Pydantic模型
class ChatRequest(BaseModel):
    query: str
    conversation_id: Optional[str] = None
    inputs: Optional[dict] = None
    files: Optional[List[str]] = None

class ChatResponse(BaseModel):
    message_id: str
    conversation_id: str
    answer: str
    metadata: Optional[dict] = None

class ConversationResponse(BaseModel):
    id: int
    dify_conversation_id: str
    title: str
    application_id: int
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True

class MessageResponse(BaseModel):
    id: int
    dify_message_id: str
    content: str
    role: str
    metadata: Optional[dict] = None
    created_at: str
    
    class Config:
        from_attributes = True

@router.post("/{app_id}/send", response_model=ChatResponse)
async def send_message(
    app_id: int,
    chat_request: ChatRequest,
    user_id: int = 1,  # 临时固定用户ID，后续需要从认证中获取
    db: Session = Depends(get_db)
):
    """发送聊天消息"""
    # 获取应用信息
    application = db.query(Application).filter(
        Application.id == app_id,
        Application.is_active == True
    ).first()
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    # 创建Dify服务实例
    dify_service = DifyService(api_key=application.dify_api_key)
    
    try:
        # 发送消息到Dify
        response = await dify_service.send_chat_message(
            query=chat_request.query,
            conversation_id=chat_request.conversation_id,
            inputs=chat_request.inputs,
            files=chat_request.files
        )
        
        # 获取或创建对话记录
        conversation = None
        if chat_request.conversation_id:
            conversation = db.query(Conversation).filter(
                Conversation.dify_conversation_id == chat_request.conversation_id
            ).first()
        
        if not conversation:
            # 创建新对话
            conversation = Conversation(
                dify_conversation_id=response["conversation_id"],
                title=chat_request.query[:50] + "..." if len(chat_request.query) > 50 else chat_request.query,
                user_id=user_id,
                application_id=app_id
            )
            db.add(conversation)
            db.commit()
            db.refresh(conversation)
        
        # 保存用户消息
        user_message = Message(
            dify_message_id=f"user_{response['message_id']}",
            conversation_id=conversation.id,
            content=chat_request.query,
            role="user"
        )
        db.add(user_message)
        
        # 保存AI回复
        ai_message = Message(
            dify_message_id=response["message_id"],
            conversation_id=conversation.id,
            content=response["answer"],
            role="assistant",
            metadata=json.dumps(response.get("metadata", {}))
        )
        db.add(ai_message)
        
        db.commit()
        
        return ChatResponse(
            message_id=response["message_id"],
            conversation_id=response["conversation_id"],
            answer=response["answer"],
            metadata=response.get("metadata")
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送消息失败: {str(e)}"
        )

@router.get("/{app_id}/conversations", response_model=List[ConversationResponse])
async def get_conversations(
    app_id: int,
    user_id: int = 1,  # 临时固定用户ID
    skip: int = 0,
    limit: int = 20,
    db: Session = Depends(get_db)
):
    """获取对话列表"""
    conversations = db.query(Conversation).filter(
        Conversation.application_id == app_id,
        Conversation.user_id == user_id
    ).order_by(Conversation.updated_at.desc()).offset(skip).limit(limit).all()
    
    return conversations

@router.get("/conversations/{conversation_id}/messages", response_model=List[MessageResponse])
async def get_conversation_messages(
    conversation_id: int,
    skip: int = 0,
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """获取对话消息列表"""
    messages = db.query(Message).filter(
        Message.conversation_id == conversation_id
    ).order_by(Message.created_at.asc()).offset(skip).limit(limit).all()
    
    # 解析metadata
    for message in messages:
        if message.metadata:
            try:
                message.metadata = json.loads(message.metadata)
            except:
                message.metadata = None
    
    return messages

@router.delete("/conversations/{conversation_id}")
async def delete_conversation(
    conversation_id: int,
    user_id: int = 1,  # 临时固定用户ID
    db: Session = Depends(get_db)
):
    """删除对话"""
    conversation = db.query(Conversation).filter(
        Conversation.id == conversation_id,
        Conversation.user_id == user_id
    ).first()
    
    if not conversation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="对话不存在"
        )
    
    # 删除对话及其消息
    db.query(Message).filter(Message.conversation_id == conversation_id).delete()
    db.delete(conversation)
    db.commit()
    
    return {"message": "对话已删除"}

@router.post("/messages/{message_id}/feedback")
async def send_message_feedback(
    message_id: str,
    rating: str,  # "like" or "dislike"
    db: Session = Depends(get_db)
):
    """发送消息反馈"""
    # 查找消息
    message = db.query(Message).filter(
        Message.dify_message_id == message_id
    ).first()
    
    if not message:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="消息不存在"
        )
    
    # 获取应用信息
    conversation = db.query(Conversation).filter(
        Conversation.id == message.conversation_id
    ).first()
    application = db.query(Application).filter(
        Application.id == conversation.application_id
    ).first()
    
    # 发送反馈到Dify
    try:
        dify_service = DifyService(api_key=application.dify_api_key)
        await dify_service.send_feedback(message_id, rating)
        return {"message": "反馈已发送"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"发送反馈失败: {str(e)}"
        )

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    user_id: int = 1,  # 临时固定用户ID
    db: Session = Depends(get_db)
):
    """上传文件"""
    # 检查文件类型
    allowed_extensions = ["png", "jpg", "jpeg", "gif", "webp", "pdf", "txt", "doc", "docx"]
    file_extension = file.filename.split(".")[-1].lower()

    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的文件类型: {file_extension}"
        )

    # 检查文件大小 (10MB)
    max_size = 10 * 1024 * 1024
    file_content = await file.read()
    if len(file_content) > max_size:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="文件大小超过10MB限制"
        )

    try:
        # 生成唯一文件名
        file_id = str(uuid.uuid4())
        filename = f"{file_id}.{file_extension}"
        file_path = f"uploads/{filename}"

        # 确保上传目录存在
        os.makedirs("uploads", exist_ok=True)

        # 保存文件到本地
        with open(file_path, "wb") as buffer:
            buffer.write(file_content)

        # 上传到Dify
        dify_service = DifyService()  # 使用默认API密钥
        dify_response = await dify_service.upload_file(file_content, file.filename)

        # 保存文件记录到数据库
        uploaded_file = UploadedFile(
            filename=filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=len(file_content),
            mime_type=file.content_type,
            dify_file_id=dify_response.get("id"),
            user_id=user_id
        )
        db.add(uploaded_file)
        db.commit()
        db.refresh(uploaded_file)

        return {
            "id": uploaded_file.id,
            "filename": uploaded_file.filename,
            "original_filename": uploaded_file.original_filename,
            "file_size": uploaded_file.file_size,
            "mime_type": uploaded_file.mime_type,
            "dify_file_id": uploaded_file.dify_file_id,
            "url": f"/uploads/{filename}"
        }

    except Exception as e:
        # 清理已保存的文件
        if os.path.exists(file_path):
            os.remove(file_path)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文件上传失败: {str(e)}"
        )
