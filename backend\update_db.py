"""
更新数据库数据
"""
import sqlite3
import os

def update_database():
    """更新数据库中的NULL值"""
    db_path = "../database/app.db"
    
    if not os.path.exists(db_path):
        print("数据库文件不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 更新icon_url为空字符串
        cursor.execute("UPDATE applications SET icon_url = '' WHERE icon_url IS NULL")
        
        # 检查更新的行数
        updated_rows = cursor.rowcount
        print(f"更新了 {updated_rows} 行的icon_url字段")
        
        # 查看当前数据
        cursor.execute("SELECT id, name, icon_url FROM applications")
        rows = cursor.fetchall()
        print("当前应用数据:")
        for row in rows:
            print(f"ID: {row[0]}, Name: {row[1]}, Icon URL: '{row[2]}'")
        
        conn.commit()
        
    except Exception as e:
        print(f"更新数据库时出错: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == "__main__":
    update_database()
