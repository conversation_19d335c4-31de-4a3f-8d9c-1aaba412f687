// API服务类
import axios from 'axios';
import { Application, Conversation, Message, ChatRequest, ChatResponse, ApplicationCreate, UploadedFile } from '../types';

const API_BASE_URL = 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 应用相关API
export const applicationApi = {
  // 获取应用列表
  getApplications: async (): Promise<Application[]> => {
    const response = await api.get('/api/applications/');
    return response.data;
  },

  // 获取单个应用
  getApplication: async (id: number): Promise<Application> => {
    const response = await api.get(`/api/applications/${id}`);
    return response.data;
  },

  // 创建应用
  createApplication: async (data: ApplicationCreate): Promise<Application> => {
    const response = await api.post('/api/applications/', data);
    return response.data;
  },

  // 更新应用
  updateApplication: async (id: number, data: Partial<ApplicationCreate>): Promise<Application> => {
    const response = await api.put(`/api/applications/${id}`, data);
    return response.data;
  },

  // 删除应用
  deleteApplication: async (id: number): Promise<void> => {
    await api.delete(`/api/applications/${id}`);
  },

  // 获取应用Dify信息
  getApplicationDifyInfo: async (id: number): Promise<any> => {
    const response = await api.get(`/api/applications/${id}/info`);
    return response.data;
  },
};

// 聊天相关API
export const chatApi = {
  // 发送消息
  sendMessage: async (appId: number, data: ChatRequest): Promise<ChatResponse> => {
    const response = await api.post(`/api/chat/${appId}/send`, data);
    return response.data;
  },

  // 获取对话列表
  getConversations: async (appId: number): Promise<Conversation[]> => {
    const response = await api.get(`/api/chat/${appId}/conversations`);
    return response.data;
  },

  // 获取对话消息
  getConversationMessages: async (conversationId: number): Promise<Message[]> => {
    const response = await api.get(`/api/chat/conversations/${conversationId}/messages`);
    return response.data;
  },

  // 删除对话
  deleteConversation: async (conversationId: number): Promise<void> => {
    await api.delete(`/api/chat/conversations/${conversationId}`);
  },

  // 发送消息反馈
  sendFeedback: async (messageId: string, rating: 'like' | 'dislike'): Promise<void> => {
    await api.post(`/api/chat/messages/${messageId}/feedback`, { rating });
  },

  // 上传文件
  uploadFile: async (file: File): Promise<UploadedFile> => {
    const formData = new FormData();
    formData.append('file', file);
    
    const response = await api.post('/api/chat/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },
};

// 系统相关API
export const systemApi = {
  // 健康检查
  healthCheck: async (): Promise<{ status: string }> => {
    const response = await api.get('/health');
    return response.data;
  },

  // 测试Dify连接
  testDifyConnection: async (): Promise<any> => {
    const response = await api.get('/test-dify-connection');
    return response.data;
  },
};

export default api;
