"""
Dify API集成服务
"""
import httpx
import json
import os
from typing import Optional, Dict, Any, List
from dotenv import load_dotenv

load_dotenv()

class DifyService:
    """Dify API服务类 - 支持外部Dify服务器"""

    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        self.api_key = api_key or os.getenv("DIFY_API_KEY")
        self.base_url = base_url or os.getenv("DIFY_API_BASE_URL", "https://api.dify.ai/v1")

        # 确保base_url不以斜杠结尾
        if self.base_url.endswith('/'):
            self.base_url = self.base_url[:-1]

        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        # 验证配置
        if not self.api_key or self.api_key == "your_actual_dify_api_key_here":
            raise ValueError("请在.env文件中配置有效的DIFY_API_KEY")

        if "your-dify-server" in self.base_url:
            raise ValueError("请在.env文件中配置有效的DIFY_API_BASE_URL")

    async def test_connection(self) -> Dict[str, Any]:
        """测试与Dify服务器的连接"""
        try:
            async with httpx.AsyncClient(timeout=10.0) as client:
                # 尝试获取应用信息来测试连接
                response = await client.get(
                    f"{self.base_url}/info",
                    headers=self.headers
                )
                if response.status_code == 200:
                    return {"status": "success", "message": "连接成功", "data": response.json()}
                else:
                    return {
                        "status": "error",
                        "message": f"连接失败: HTTP {response.status_code}",
                        "details": response.text
                    }
        except httpx.ConnectError:
            return {
                "status": "error",
                "message": f"无法连接到Dify服务器: {self.base_url}",
                "details": "请检查服务器地址是否正确，以及服务器是否正在运行"
            }
        except httpx.TimeoutException:
            return {
                "status": "error",
                "message": "连接超时",
                "details": "请检查网络连接或服务器响应速度"
            }
        except Exception as e:
            return {
                "status": "error",
                "message": f"连接测试失败: {str(e)}",
                "details": "请检查API密钥和服务器配置"
            }
    
    async def send_chat_message(
        self, 
        query: str, 
        conversation_id: Optional[str] = None,
        user: str = "user",
        inputs: Optional[Dict] = None,
        response_mode: str = "blocking",
        files: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """发送聊天消息"""
        url = f"{self.base_url}/chat-messages"
        
        payload = {
            "inputs": inputs or {},
            "query": query,
            "response_mode": response_mode,
            "user": user
        }
        
        if conversation_id:
            payload["conversation_id"] = conversation_id
        
        if files:
            payload["files"] = files
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
    
    async def get_conversation_messages(
        self, 
        conversation_id: str,
        user: str = "user",
        first_id: Optional[str] = None,
        limit: int = 20
    ) -> Dict[str, Any]:
        """获取对话消息列表"""
        url = f"{self.base_url}/messages"
        
        params = {
            "conversation_id": conversation_id,
            "user": user,
            "limit": limit
        }
        
        if first_id:
            params["first_id"] = first_id
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
    
    async def upload_file(self, file_content: bytes, filename: str, user: str = "user") -> Dict[str, Any]:
        """上传文件"""
        url = f"{self.base_url}/files/upload"
        
        # 移除Content-Type头，让httpx自动设置multipart/form-data
        headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
        
        files = {
            "file": (filename, file_content)
        }
        
        data = {
            "user": user
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=headers, files=files, data=data)
            response.raise_for_status()
            return response.json()
    
    async def get_application_info(self) -> Dict[str, Any]:
        """获取应用基本信息"""
        url = f"{self.base_url}/info"
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
    
    async def get_application_parameters(self) -> Dict[str, Any]:
        """获取应用参数信息"""
        url = f"{self.base_url}/parameters"
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers)
            response.raise_for_status()
            return response.json()
    
    async def stop_chat_generation(self, task_id: str, user: str = "user") -> Dict[str, Any]:
        """停止聊天消息生成"""
        url = f"{self.base_url}/chat-messages/{task_id}/stop"
        
        payload = {
            "user": user
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
    
    async def get_suggested_questions(
        self, 
        message_id: str, 
        user: str = "user"
    ) -> Dict[str, Any]:
        """获取建议问题"""
        url = f"{self.base_url}/messages/{message_id}/suggested"
        
        params = {
            "user": user
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            return response.json()
    
    async def send_feedback(
        self, 
        message_id: str, 
        rating: str, 
        user: str = "user"
    ) -> Dict[str, Any]:
        """发送消息反馈"""
        url = f"{self.base_url}/messages/{message_id}/feedbacks"
        
        payload = {
            "rating": rating,  # "like" or "dislike"
            "user": user
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, headers=self.headers, json=payload)
            response.raise_for_status()
            return response.json()
