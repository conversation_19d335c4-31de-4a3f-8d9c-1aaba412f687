# CREC AI Manager

一个基于 React 和 FastAPI 的 AI 程序管理和使用平台，集成 Dify API。

## 项目概述

CREC AI Manager 是一个完整的前后端分离的 Web 应用，允许用户管理和使用多个 AI 应用程序。通过集成 Dify API，用户可以轻松地创建、配置和使用各种 AI 助手。

## 功能特性

### 🎯 核心功能

- **应用程序展示** - 卡片式展示所有可用的 AI 应用
- **智能聊天** - 与 AI 应用进行实时对话，支持历史记录
- **应用管理** - 创建、编辑、删除 AI 应用
- **文件上传** - 支持图片和文档上传，增强 AI 交互
- **对话管理** - 管理多个对话会话，支持对话删除

### 🔧 技术特性

- **外部 Dify 集成** - 支持连接到任何 Dify 服务器实例
- **响应式设计** - 适配桌面和移动设备
- **实时更新** - 热重载开发环境
- **类型安全** - 完整的 TypeScript 支持
- **API 优先** - RESTful API 设计

## 技术栈

### 前端

- **React 18** - 现代化的用户界面框架
- **TypeScript** - 类型安全的 JavaScript
- **Vite** - 快速的构建工具
- **Tailwind CSS** - 实用优先的 CSS 框架
- **React Router** - 客户端路由
- **Axios** - HTTP 客户端

### 后端

- **FastAPI** - 现代化的 Python Web 框架
- **SQLAlchemy** - Python SQL 工具包和 ORM
- **SQLite** - 轻量级数据库
- **Pydantic** - 数据验证和设置管理
- **HTTPX** - 异步 HTTP 客户端
- **Python-multipart** - 文件上传支持

## 项目结构

```
crec-ai-manager/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   ├── pages/          # 页面组件
│   │   ├── services/       # API服务
│   │   ├── types/          # TypeScript类型定义
│   │   └── App.tsx         # 主应用组件
│   ├── public/             # 静态资源
│   └── package.json        # 前端依赖
├── backend/                 # FastAPI后端服务
│   ├── routers/            # API路由
│   ├── models.py           # 数据库模型
│   ├── database.py         # 数据库配置
│   ├── dify_service.py     # Dify API集成
│   ├── main.py             # 主应用文件
│   └── requirements.txt    # 后端依赖
├── database/               # SQLite数据库文件
├── docs/                   # 项目文档
└── README.md              # 项目说明
```

## 快速开始

### 环境要求

- Node.js 16+
- Python 3.9+
- npm 或 yarn

### 1. 克隆项目

```bash
git clone <repository-url>
cd crec-ai-manager
```

### 2. 配置 Dify 连接

编辑 `backend/.env` 文件：

```bash
# 替换为您的Dify服务器地址和API密钥
DIFY_API_BASE_URL=http://your-dify-server:port/v1
DIFY_API_KEY=your_actual_api_key_here

# 数据库配置
DATABASE_URL=sqlite:///../database/app.db
```

### 3. 启动后端服务

```bash
cd backend
pip install -r requirements.txt
python init_db.py  # 初始化数据库
python -m uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 4. 启动前端应用

```bash
cd frontend
npm install
npm run dev
```

### 5. 访问应用

- 前端: http://localhost:5173
- 后端 API 文档: http://localhost:8000/docs
- Dify 连接测试: http://localhost:8000/test-dify-connection
