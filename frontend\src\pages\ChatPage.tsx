import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import type { Application, Conversation, Message } from "../types/index.js";
import { applicationApi, chatApi } from "../services/api";
import MessageBubble from "../components/MessageBubble";
import ChatInput from "../components/ChatInput";

const ChatPage: React.FC = () => {
  const { appId } = useParams<{ appId: string }>();
  const navigate = useNavigate();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const [application, setApplication] = useState<Application | null>(null);
  const [conversations, setConversations] = useState<Conversation[]>([]);
  const [currentConversation, setCurrentConversation] =
    useState<Conversation | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [sending, setSending] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (appId) {
      loadApplication(parseInt(appId));
      loadConversations(parseInt(appId));
    }
  }, [appId]);

  useEffect(() => {
    if (currentConversation) {
      loadMessages(currentConversation.id);
    }
  }, [currentConversation]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  const loadApplication = async (id: number) => {
    try {
      const app = await applicationApi.getApplication(id);
      setApplication(app);
    } catch (err) {
      setError("加载应用信息失败");
      console.error("Error loading application:", err);
    }
  };

  const loadConversations = async (appId: number) => {
    try {
      const convs = await chatApi.getConversations(appId);
      setConversations(convs);
      if (convs.length > 0) {
        setCurrentConversation(convs[0]);
      }
    } catch (err) {
      console.error("Error loading conversations:", err);
    } finally {
      setLoading(false);
    }
  };

  const loadMessages = async (conversationId: number) => {
    try {
      const msgs = await chatApi.getConversationMessages(conversationId);
      setMessages(msgs);
    } catch (err) {
      console.error("Error loading messages:", err);
    }
  };

  const handleSendMessage = async (content: string, files?: File[]) => {
    if (!appId || !content.trim()) return;

    setSending(true);
    try {
      // 上传文件（如果有）
      const uploadedFiles = [];
      if (files && files.length > 0) {
        for (const file of files) {
          const uploadedFile = await chatApi.uploadFile(file);
          uploadedFiles.push(uploadedFile.dify_file_id);
        }
      }

      // 发送消息
      const response = await chatApi.sendMessage(parseInt(appId), {
        query: content,
        conversation_id: currentConversation?.dify_conversation_id,
        files: uploadedFiles.filter(Boolean) as string[],
      });

      // 重新加载对话列表和消息
      await loadConversations(parseInt(appId));

      // 如果是新对话，设置当前对话
      if (!currentConversation) {
        const newConversations = await chatApi.getConversations(
          parseInt(appId)
        );
        const newConv = newConversations.find(
          (c) => c.dify_conversation_id === response.conversation_id
        );
        if (newConv) {
          setCurrentConversation(newConv);
        }
      } else {
        // 重新加载当前对话的消息
        await loadMessages(currentConversation.id);
      }
    } catch (err) {
      alert("发送消息失败");
      console.error("Error sending message:", err);
    } finally {
      setSending(false);
    }
  };

  const handleFeedback = async (
    messageId: string,
    rating: "like" | "dislike"
  ) => {
    try {
      await chatApi.sendFeedback(messageId, rating);
    } catch (err) {
      console.error("Error sending feedback:", err);
    }
  };

  const handleNewConversation = () => {
    setCurrentConversation(null);
    setMessages([]);
  };

  const handleSelectConversation = (conversation: Conversation) => {
    setCurrentConversation(conversation);
  };

  const handleDeleteConversation = async (conversation: Conversation) => {
    if (window.confirm("确定要删除这个对话吗？")) {
      try {
        await chatApi.deleteConversation(conversation.id);
        await loadConversations(parseInt(appId!));
        if (currentConversation?.id === conversation.id) {
          setCurrentConversation(null);
          setMessages([]);
        }
      } catch (err) {
        alert("删除对话失败");
        console.error("Error deleting conversation:", err);
      }
    }
  };

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => navigate("/")}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            返回首页
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex">
      {/* 侧边栏 - 对话列表 */}
      <div className="w-80 bg-gray-100 border-r flex flex-col">
        <div className="p-4 border-b bg-white">
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => navigate("/")}
              className="text-gray-600 hover:text-gray-800"
            >
              ← 返回
            </button>
            <button
              onClick={handleNewConversation}
              className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
            >
              新对话
            </button>
          </div>

          {application && (
            <div className="flex items-center">
              {application.icon_url ? (
                <img
                  src={application.icon_url}
                  alt={application.name}
                  className="w-8 h-8 rounded mr-3"
                />
              ) : (
                <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center mr-3">
                  <span className="text-white text-sm font-bold">
                    {application.name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              <div>
                <h2 className="font-semibold text-gray-900">
                  {application.name}
                </h2>
                <p className="text-sm text-gray-600">{application.app_type}</p>
              </div>
            </div>
          )}
        </div>

        <div className="flex-1 overflow-y-auto">
          {conversations.length === 0 ? (
            <div className="p-4 text-center text-gray-500">暂无对话记录</div>
          ) : (
            conversations.map((conversation) => (
              <div
                key={conversation.id}
                className={`p-4 border-b cursor-pointer hover:bg-gray-50 ${
                  currentConversation?.id === conversation.id
                    ? "bg-blue-50 border-blue-200"
                    : ""
                }`}
                onClick={() => handleSelectConversation(conversation)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium text-gray-900 truncate">
                      {conversation.title}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {new Date(conversation.updated_at).toLocaleDateString()}
                    </p>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteConversation(conversation);
                    }}
                    className="text-gray-400 hover:text-red-500 ml-2"
                  >
                    🗑️
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 消息区域 */}
        <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
          {messages.length === 0 ? (
            <div className="h-full flex items-center justify-center">
              <div className="text-center">
                <div className="text-gray-400 text-6xl mb-4">💬</div>
                <h3 className="text-xl font-medium text-gray-900 mb-2">
                  {currentConversation ? "暂无消息" : "开始新对话"}
                </h3>
                <p className="text-gray-600">
                  {currentConversation
                    ? "发送消息开始对话"
                    : "输入消息开始与AI助手对话"}
                </p>
              </div>
            </div>
          ) : (
            <>
              {messages.map((message) => (
                <MessageBubble
                  key={message.id}
                  message={message}
                  onFeedback={handleFeedback}
                />
              ))}
              <div ref={messagesEndRef} />
            </>
          )}
        </div>

        {/* 输入区域 */}
        <ChatInput
          onSendMessage={handleSendMessage}
          disabled={sending}
          placeholder={sending ? "发送中..." : "输入消息..."}
        />
      </div>
    </div>
  );
};

export default ChatPage;
