"""
数据库初始化脚本
"""
import os
import sys
from sqlalchemy import create_engine
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Base
from database import engine

def init_database():
    """初始化数据库"""
    load_dotenv()
    
    # 确保数据库目录存在
    db_dir = os.path.dirname("../database/app.db")
    os.makedirs(db_dir, exist_ok=True)
    
    # 创建所有表
    print("正在创建数据库表...")
    Base.metadata.create_all(bind=engine)
    print("数据库表创建完成！")
    
    # 创建默认数据
    create_default_data()

def create_default_data():
    """创建默认数据"""
    from sqlalchemy.orm import sessionmaker
    from models import User, Application
    from passlib.context import CryptContext
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    # 密码加密上下文
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    
    try:
        # 检查是否已有用户
        existing_user = db.query(User).first()
        if not existing_user:
            # 创建默认管理员用户
            admin_user = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=pwd_context.hash("admin123"),
                is_active=True
            )
            db.add(admin_user)
            print("创建默认管理员用户: admin / admin123")
        
        # 检查是否已有应用
        existing_app = db.query(Application).first()
        if not existing_app:
            # 创建示例应用
            demo_app = Application(
                name="示例聊天助手",
                description="这是一个示例AI聊天助手应用",
                dify_app_id="demo_app_id",
                dify_api_key="demo_api_key",
                app_type="chat",
                is_active=True
            )
            db.add(demo_app)
            print("创建示例应用")
        
        db.commit()
        print("默认数据创建完成！")
        
    except Exception as e:
        print(f"创建默认数据时出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    init_database()
