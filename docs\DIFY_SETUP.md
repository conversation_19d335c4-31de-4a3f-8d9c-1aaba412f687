# Dify服务器配置说明

本项目支持连接到外部部署的Dify服务器。请按照以下步骤配置您的Dify连接。

## 配置步骤

### 1. 获取Dify服务器信息

首先，您需要确定您的Dify服务器的访问地址和端口：

- **自部署Dify**: 通常是 `http://your-server-ip:port` 或 `https://your-domain.com`
- **Dify Cloud**: 使用 `https://api.dify.ai/v1`

### 2. 获取API密钥

在您的Dify管理界面中：

1. 登录到Dify控制台
2. 选择您要集成的应用
3. 进入应用设置 → API访问
4. 复制API密钥

### 3. 配置环境变量

编辑 `backend/.env` 文件，更新以下配置：

```bash
# Dify API配置
DIFY_API_BASE_URL=http://your-dify-server:port/v1
DIFY_API_KEY=your_actual_api_key_here
```

**配置示例：**

```bash
# 示例1: 自部署Dify (HTTP)
DIFY_API_BASE_URL=http://*************:80/v1
DIFY_API_KEY=app-1234567890abcdef

# 示例2: 自部署Dify (HTTPS)
DIFY_API_BASE_URL=https://dify.yourcompany.com/v1
DIFY_API_KEY=app-1234567890abcdef

# 示例3: Dify Cloud
DIFY_API_BASE_URL=https://api.dify.ai/v1
DIFY_API_KEY=app-1234567890abcdef
```

### 4. 测试连接

启动后端服务后，您可以通过以下方式测试连接：

```bash
# 方法1: 使用curl测试
curl http://localhost:8000/test-dify-connection

# 方法2: 在浏览器中访问
http://localhost:8000/test-dify-connection
```

成功的响应示例：
```json
{
  "status": "success",
  "message": "连接成功",
  "data": {
    "name": "Your App Name",
    "description": "Your App Description"
  }
}
```

失败的响应示例：
```json
{
  "status": "error",
  "message": "无法连接到Dify服务器",
  "details": "请检查服务器地址是否正确"
}
```

## 常见问题

### Q1: 连接超时
**问题**: 提示"连接超时"
**解决方案**: 
- 检查Dify服务器是否正在运行
- 确认网络连接正常
- 检查防火墙设置

### Q2: 401 Unauthorized
**问题**: 提示"HTTP 401"错误
**解决方案**:
- 检查API密钥是否正确
- 确认API密钥是否已过期
- 验证应用是否已发布

### Q3: 404 Not Found
**问题**: 提示"HTTP 404"错误
**解决方案**:
- 检查API基础URL是否正确
- 确认URL路径包含 `/v1`
- 验证Dify版本是否支持该API

### Q4: 网络连接错误
**问题**: 提示"无法连接到Dify服务器"
**解决方案**:
- 检查服务器IP地址和端口
- 确认服务器可以从当前网络访问
- 检查是否需要配置代理

## 支持的Dify版本

本项目支持以下Dify版本：
- Dify 0.6.0+
- Dify Cloud (最新版本)

## API端点说明

本项目使用以下Dify API端点：

- `GET /info` - 获取应用基本信息
- `POST /chat-messages` - 发送聊天消息
- `GET /messages` - 获取对话历史
- `POST /files/upload` - 上传文件
- `POST /messages/{message_id}/feedbacks` - 发送反馈

确保您的Dify应用支持这些API功能。
