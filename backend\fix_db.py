"""
修复数据库中的NULL值
"""
import os
import sys
from sqlalchemy.orm import sessionmaker
from dotenv import load_dotenv

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import Application
from database import engine

def fix_database():
    """修复数据库中的NULL值"""
    load_dotenv()
    
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # 修复icon_url为NULL的记录
        applications = db.query(Application).filter(Application.icon_url.is_(None)).all()
        for app in applications:
            app.icon_url = ""
        
        db.commit()
        print(f"修复了 {len(applications)} 个应用的icon_url字段")
        
    except Exception as e:
        print(f"修复数据库时出错: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    fix_database()
