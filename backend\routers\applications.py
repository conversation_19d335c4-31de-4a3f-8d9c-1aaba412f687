"""
应用管理相关API路由
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime

from database import get_db
from models import Application
from dify_service import DifyService

router = APIRouter(prefix="/api/applications", tags=["applications"])

# Pydantic模型
class ApplicationCreate(BaseModel):
    name: str
    description: str = ""
    dify_app_id: str
    dify_api_key: str
    app_type: str = "chat"
    icon_url: str = ""

class ApplicationUpdate(BaseModel):
    name: str = None
    description: str = None
    dify_api_key: str = None
    app_type: str = None
    icon_url: str = None
    is_active: bool = None

class ApplicationResponse(BaseModel):
    id: int
    name: str
    description: str
    dify_app_id: str
    app_type: str
    icon_url: Optional[str] = None
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

@router.get("/", response_model=List[ApplicationResponse])
async def get_applications(
    skip: int = 0, 
    limit: int = 100, 
    db: Session = Depends(get_db)
):
    """获取应用列表"""
    applications = db.query(Application).filter(
        Application.is_active == True
    ).offset(skip).limit(limit).all()
    return applications

@router.get("/{app_id}", response_model=ApplicationResponse)
async def get_application(app_id: int, db: Session = Depends(get_db)):
    """获取单个应用信息"""
    application = db.query(Application).filter(Application.id == app_id).first()
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    return application

@router.post("/", response_model=ApplicationResponse)
async def create_application(
    application: ApplicationCreate, 
    db: Session = Depends(get_db)
):
    """创建新应用"""
    # 验证Dify API密钥是否有效
    try:
        dify_service = DifyService(api_key=application.dify_api_key)
        await dify_service.get_application_info()
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Dify API密钥无效: {str(e)}"
        )
    
    # 检查dify_app_id是否已存在
    existing_app = db.query(Application).filter(
        Application.dify_app_id == application.dify_app_id
    ).first()
    if existing_app:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="该Dify应用ID已存在"
        )
    
    # 创建应用
    db_application = Application(**application.model_dump())
    db.add(db_application)
    db.commit()
    db.refresh(db_application)
    return db_application

@router.put("/{app_id}", response_model=ApplicationResponse)
async def update_application(
    app_id: int, 
    application: ApplicationUpdate, 
    db: Session = Depends(get_db)
):
    """更新应用信息"""
    db_application = db.query(Application).filter(Application.id == app_id).first()
    if not db_application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    # 如果更新了API密钥，验证其有效性
    if application.dify_api_key:
        try:
            dify_service = DifyService(api_key=application.dify_api_key)
            await dify_service.get_application_info()
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Dify API密钥无效: {str(e)}"
            )
    
    # 更新字段
    update_data = application.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_application, field, value)
    
    db.commit()
    db.refresh(db_application)
    return db_application

@router.delete("/{app_id}")
async def delete_application(app_id: int, db: Session = Depends(get_db)):
    """删除应用（软删除）"""
    db_application = db.query(Application).filter(Application.id == app_id).first()
    if not db_application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    # 软删除
    db_application.is_active = False
    db.commit()
    return {"message": "应用已删除"}

@router.get("/{app_id}/info")
async def get_application_dify_info(app_id: int, db: Session = Depends(get_db)):
    """获取应用的Dify信息"""
    application = db.query(Application).filter(Application.id == app_id).first()
    if not application:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="应用不存在"
        )
    
    try:
        dify_service = DifyService(api_key=application.dify_api_key)
        info = await dify_service.get_application_info()
        return info
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Dify应用信息失败: {str(e)}"
        )
