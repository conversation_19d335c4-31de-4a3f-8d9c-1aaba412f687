import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import type { Application } from "../types/index.js";
import { applicationApi, systemApi } from "../services/api";
import ApplicationCard from "../components/ApplicationCard";

const AdminPage: React.FC = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [difyStatus, setDifyStatus] = useState<any>(null);
  const navigate = useNavigate();

  useEffect(() => {
    loadApplications();
    checkDifyConnection();
  }, []);

  const loadApplications = async () => {
    try {
      setLoading(true);
      const apps = await applicationApi.getApplications();
      setApplications(apps);
    } catch (err) {
      console.error("Error loading applications:", err);
    } finally {
      setLoading(false);
    }
  };

  const checkDifyConnection = async () => {
    try {
      const status = await systemApi.testDifyConnection();
      setDifyStatus(status);
    } catch (err) {
      console.error("Error checking Dify connection:", err);
    }
  };

  const handleEditApplication = (app: Application) => {
    navigate(`/admin/applications/${app.id}/edit`);
  };

  const handleDeleteApplication = async (app: Application) => {
    if (window.confirm(`确定要删除应用 "${app.name}" 吗？`)) {
      try {
        await applicationApi.deleteApplication(app.id);
        await loadApplications();
      } catch (err) {
        alert("删除应用失败");
        console.error("Error deleting application:", err);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">管理中心</h1>
            <p className="text-gray-600 mt-2">管理您的AI应用</p>
          </div>
          <div className="space-x-4">
            <button
              onClick={() => navigate("/")}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              返回首页
            </button>
            <button
              onClick={() => navigate("/admin/applications/new")}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              添加应用
            </button>
          </div>
        </div>

        {/* Dify连接状态 */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-lg font-semibold mb-4">Dify服务器状态</h2>
            {difyStatus ? (
              <div
                className={`flex items-center ${
                  difyStatus.status === "success"
                    ? "text-green-600"
                    : "text-red-600"
                }`}
              >
                <div
                  className={`w-3 h-3 rounded-full mr-3 ${
                    difyStatus.status === "success"
                      ? "bg-green-500"
                      : "bg-red-500"
                  }`}
                ></div>
                <div>
                  <p className="font-medium">{difyStatus.message}</p>
                  {difyStatus.details && (
                    <p className="text-sm text-gray-600 mt-1">
                      {difyStatus.details}
                    </p>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-gray-500">检查中...</div>
            )}
            <button
              onClick={checkDifyConnection}
              className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 text-sm"
            >
              重新检查
            </button>
          </div>
        </div>

        {/* 应用列表 */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b">
            <h2 className="text-lg font-semibold">应用列表</h2>
          </div>

          {loading ? (
            <div className="p-8 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
              <p className="mt-4 text-gray-600">加载中...</p>
            </div>
          ) : applications.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-400 text-4xl mb-4">🤖</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                暂无应用
              </h3>
              <p className="text-gray-600 mb-4">创建您的第一个AI应用</p>
              <button
                onClick={() => navigate("/admin/applications/new")}
                className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                创建应用
              </button>
            </div>
          ) : (
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {applications.map((app) => (
                  <ApplicationCard
                    key={app.id}
                    application={app}
                    onSelect={() => navigate(`/chat/${app.id}`)}
                    onEdit={handleEditApplication}
                    onDelete={handleDeleteApplication}
                  />
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 系统信息 */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold mb-4">系统信息</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">应用总数:</span>
              <span className="ml-2 font-medium">{applications.length}</span>
            </div>
            <div>
              <span className="text-gray-600">活跃应用:</span>
              <span className="ml-2 font-medium">
                {applications.filter((app) => app.is_active).length}
              </span>
            </div>
            <div>
              <span className="text-gray-600">版本:</span>
              <span className="ml-2 font-medium">v1.0.0</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminPage;
