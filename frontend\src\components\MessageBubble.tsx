import React from "react";
import type { Message } from "../types/index.js";

interface MessageBubbleProps {
  message: Message;
  onFeedback?: (messageId: string, rating: "like" | "dislike") => void;
}

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  onFeedback,
}) => {
  const isUser = message.role === "user";

  return (
    <div className={`flex ${isUser ? "justify-end" : "justify-start"} mb-4`}>
      <div
        className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
          isUser ? "bg-blue-500 text-white" : "bg-gray-200 text-gray-900"
        }`}
      >
        <div className="whitespace-pre-wrap">{message.content}</div>

        {!isUser && onFeedback && (
          <div className="flex justify-end mt-2 space-x-2">
            <button
              onClick={() => onFeedback(message.dify_message_id, "like")}
              className="text-gray-500 hover:text-green-500 text-sm"
              title="有用"
            >
              👍
            </button>
            <button
              onClick={() => onFeedback(message.dify_message_id, "dislike")}
              className="text-gray-500 hover:text-red-500 text-sm"
              title="无用"
            >
              👎
            </button>
          </div>
        )}

        <div
          className={`text-xs mt-1 ${
            isUser ? "text-blue-100" : "text-gray-500"
          }`}
        >
          {new Date(message.created_at).toLocaleTimeString()}
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;
