import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import type { Application } from "../types/index.js";
import { applicationApi } from "../services/api";
import ApplicationCard from "../components/ApplicationCard";

const ApplicationList: React.FC = () => {
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  useEffect(() => {
    loadApplications();
  }, []);

  const loadApplications = async () => {
    try {
      setLoading(true);
      const apps = await applicationApi.getApplications();
      setApplications(apps);
      setError(null);
    } catch (err) {
      setError("加载应用列表失败");
      console.error("Error loading applications:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleSelectApplication = (app: Application) => {
    navigate(`/chat/${app.id}`);
  };

  const handleEditApplication = (app: Application) => {
    navigate(`/admin/applications/${app.id}/edit`);
  };

  const handleDeleteApplication = async (app: Application) => {
    if (window.confirm(`确定要删除应用 "${app.name}" 吗？`)) {
      try {
        await applicationApi.deleteApplication(app.id);
        await loadApplications(); // 重新加载列表
      } catch (err) {
        alert("删除应用失败");
        console.error("Error deleting application:", err);
      }
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-500 text-xl mb-4">⚠️</div>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadApplications}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">AI应用中心</h1>
            <p className="text-gray-600 mt-2">选择一个AI应用开始对话</p>
          </div>
          <div className="space-x-4">
            <button
              onClick={() => navigate("/admin")}
              className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              管理中心
            </button>
            <button
              onClick={() => navigate("/admin/applications/new")}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              添加应用
            </button>
          </div>
        </div>

        {applications.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">🤖</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">
              暂无AI应用
            </h3>
            <p className="text-gray-600 mb-6">创建您的第一个AI应用开始使用</p>
            <button
              onClick={() => navigate("/admin/applications/new")}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
            >
              创建应用
            </button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {applications.map((app) => (
              <ApplicationCard
                key={app.id}
                application={app}
                onSelect={handleSelectApplication}
                onEdit={handleEditApplication}
                onDelete={handleDeleteApplication}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default ApplicationList;
